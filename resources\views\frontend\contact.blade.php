@extends('layouts.app')

@section('title', 'ติดต่อเรา - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@push('styles')
<style>
.contact-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.contact-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.contact-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: #fff;
}

.btn-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.contact-info-item {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.contact-info-item:hover {
    transform: translateX(5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.1);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-right: 15px;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.social-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50px;
    padding: 10px 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    margin: 5px;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.social-btn.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
}

.social-btn.line {
    background: linear-gradient(135deg, #00c300 0%, #00e600 100%);
}

.business-hours-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 25px;
    position: relative;
    overflow: hidden;
}

.business-hours-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.hours-item:last-child {
    border-bottom: none;
}

.status-badge {
    background: rgba(255,255,255,0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-badge.open {
    background: rgba(76, 175, 80, 0.3);
    color: #4caf50;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-elements::before,
.floating-elements::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.floating-elements::before {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-elements::after {
    bottom: 10%;
    right: 10%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}
</style>
@endpush

@section('content')
<!-- Modern Hero Section -->
<section class="contact-hero py-5 position-relative">
    <div class="floating-elements"></div>
    <div class="container position-relative">
        <div class="text-center text-white">
            <div class="mb-4">
                <i class="fas fa-comments fa-4x mb-3" style="opacity: 0.8;"></i>
            </div>
            <h1 class="display-4 fw-bold mb-4">ติดต่อเรา</h1>
            <p class="lead mb-0">เราพร้อมให้คำปรึกษาและดูแลท่านด้วยเทคโนโลยีที่ทันสมัย</p>
            <div class="mt-4">
                <span class="badge bg-light text-dark px-3 py-2 me-2">
                    <i class="fas fa-clock me-1"></i>ตอบกลับภายใน 24 ชม.
                </span>
                <span class="badge bg-light text-dark px-3 py-2">
                    <i class="fas fa-shield-alt me-1"></i>ข้อมูลปลอดภัย 100%
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Modern Contact Section -->
<section class="py-5 bg-light">
    <div class="container">
        @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show contact-card" role="alert">
            <div class="d-flex align-items-center">
                <div class="contact-icon me-3">
                    <i class="fas fa-check"></i>
                </div>
                <div>
                    <h6 class="mb-0">ส่งข้อความสำเร็จ!</h6>
                    <small>{{ session('success') }}</small>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        @endif

        <div class="row g-5">
            <!-- Modern Contact Form -->
            <div class="col-lg-8">
                <div class="contact-card p-5">
                    <div class="text-center mb-5">
                        <h3 class="section-title">ส่งข้อความถึงเรา</h3>
                        <p class="text-muted">กรอกข้อมูลด้านล่าง เราจะติดต่อกลับภายใน 24 ชั่วโมง</p>
                    </div>

                    <form action="{{ route('contact.store') }}" method="POST" id="contactForm">
                        @csrf
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name') }}" placeholder="ชื่อ-นามสกุล" required>
                                    <label for="name">
                                        <i class="fas fa-user me-2"></i>ชื่อ-นามสกุล <span class="text-danger">*</span>
                                    </label>
                                    @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                           id="email" name="email" value="{{ old('email') }}" placeholder="อีเมล" required>
                                    <label for="email">
                                        <i class="fas fa-envelope me-2"></i>อีเมล <span class="text-danger">*</span>
                                    </label>
                                    @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                           id="phone" name="phone" value="{{ old('phone') }}" placeholder="เบอร์โทรศัพท์">
                                    <label for="phone">
                                        <i class="fas fa-phone me-2"></i>เบอร์โทรศัพท์
                                    </label>
                                    @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-control @error('subject') is-invalid @enderror" id="subject" name="subject" required>
                                        <option value="">เลือกหัวข้อ</option>
                                        <option value="สอบถามบริการ" {{ old('subject') == 'สอบถามบริการ' ? 'selected' : '' }}>สอบถามบริการ</option>
                                        <option value="สอบถามแพคเกจ" {{ old('subject') == 'สอบถามแพคเกจ' ? 'selected' : '' }}>สอบถามแพคเกจ</option>
                                        <option value="ขอใบเสนอราคา" {{ old('subject') == 'ขอใบเสนอราคา' ? 'selected' : '' }}>ขอใบเสนอราคา</option>
                                        <option value="ร้องเรียน/แนะนำ" {{ old('subject') == 'ร้องเรียน/แนะนำ' ? 'selected' : '' }}>ร้องเรียน/แนะนำ</option>
                                        <option value="อื่นๆ" {{ old('subject') == 'อื่นๆ' ? 'selected' : '' }}>อื่นๆ</option>
                                    </select>
                                    <label for="subject">
                                        <i class="fas fa-tag me-2"></i>หัวข้อ <span class="text-danger">*</span>
                                    </label>
                                    @error('subject')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-floating">
                                    <textarea class="form-control @error('message') is-invalid @enderror"
                                              id="message" name="message" placeholder="ข้อความ" style="height: 120px" required>{{ old('message') }}</textarea>
                                    <label for="message">
                                        <i class="fas fa-comment me-2"></i>ข้อความ <span class="text-danger">*</span>
                                    </label>
                                    @error('message')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-modern btn-lg px-5">
                                    <i class="fas fa-paper-plane me-2"></i>ส่งข้อความ
                                </button>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt me-1"></i>
                                        ข้อมูลของคุณจะถูกเก็บรักษาอย่างปลอดภัย
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Modern Contact Info -->
            <div class="col-lg-4">
                <!-- Contact Information Card -->
                <div class="contact-card p-4 mb-4">
                    <div class="text-center mb-4">
                        <h4 class="section-title">ข้อมูลติดต่อ</h4>
                        <p class="text-muted">ช่องทางการติดต่อที่สะดวกสำหรับคุณ</p>
                    </div>

                    <div class="contact-info">
                        <div class="contact-info-item">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">ที่อยู่</h6>
                                    <p class="mb-0 text-muted small">{{ $settings['contact_address'] ?? 'ที่อยู่บริษัท' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">โทรศัพท์</h6>
                                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="text-decoration-none text-muted small">
                                        {{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">อีเมล</h6>
                                    <a href="mailto:{{ $settings['contact_email'] ?? '' }}" class="text-decoration-none text-muted small">
                                        {{ $settings['contact_email'] ?? '<EMAIL>' }}
                                    </a>
                                </div>
                            </div>
                        </div>

                        @if(!empty($settings['facebook_url']) || !empty($settings['line_id']))
                        <div class="contact-info-item">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon">
                                    <i class="fas fa-share-alt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-2 fw-bold">ติดตามเรา</h6>
                                    <div>
                                        @if(!empty($settings['facebook_url']))
                                        <a href="{{ $settings['facebook_url'] }}" class="social-btn facebook" target="_blank">
                                            <i class="fab fa-facebook me-2"></i>Facebook
                                        </a>
                                        @endif
                                        @if(!empty($settings['line_id']))
                                        <a href="https://line.me/ti/p/{{ $settings['line_id'] }}" class="social-btn line" target="_blank">
                                            <i class="fab fa-line me-2"></i>Line Official
                                        </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Modern Business Hours -->
                <div class="business-hours-card position-relative">
                    <div class="text-center mb-4">
                        <i class="fas fa-clock fa-2x mb-3" style="opacity: 0.8;"></i>
                        <h5 class="fw-bold">เวลาให้บริการ</h5>
                        <p class="mb-0 small" style="opacity: 0.9;">พร้อมให้บริการคุณตลอดเวลา</p>
                    </div>

                    <div class="text-center mb-4">
                        <div class="status-badge open">
                            <i class="fas fa-circle me-1"></i>
                            <strong>เปิดให้บริการ 24/7</strong>
                        </div>
                    </div>

                    <div class="hours-list">
                        <div class="hours-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-building me-2"></i>
                                <span>สำนักงาน</span>
                            </div>
                            <span class="status-badge">08:00 - 17:00</span>
                        </div>

                        <div class="hours-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-ambulance me-2"></i>
                                <span>บริการฉุกเฉิน</span>
                            </div>
                            <span class="status-badge open">24 ชั่วโมง</span>
                        </div>

                        <div class="hours-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <span>วันหยุดนักขัตฤกษ์</span>
                            </div>
                            <span class="status-badge open">ให้บริการ</span>
                        </div>

                        <div class="hours-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-headset me-2"></i>
                                <span>ศูนย์บริการลูกค้า</span>
                            </div>
                            <span class="status-badge open">24/7</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation and enhancement
    const form = document.getElementById('contactForm');
    const inputs = form.querySelectorAll('input, textarea, select');

    // Add floating label animation
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });

        // Check if input has value on load
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
    });

    // Form submission with loading state
    form.addEventListener('submit', function(e) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังส่ง...';
        submitBtn.disabled = true;

        // Re-enable button after 3 seconds (in case of error)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Add smooth scroll to form if there are errors
    @if($errors->any())
    document.getElementById('contactForm').scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    });
    @endif
});
</script>
@endpush

@endsection
