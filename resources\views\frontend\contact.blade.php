@extends('layouts.app')

@section('title', 'ติดต่อเรา - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@push('styles')
<style>
/* Modern Contact Page Styles - ใช้ธีมสีเดียวกับเว็บไซต์ */
.contact-hero {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    position: relative;
    overflow: hidden;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.contact-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(100, 116, 139, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(71, 85, 105, 0.05) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(71,85,105,0.03)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    z-index: 1;
}

.contact-hero::after {
    content: '';
    position: absolute;
    top: 10%;
    right: 10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(100, 116, 139, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: floatSlow 20s ease-in-out infinite;
    z-index: 1;
}

@keyframes floatSlow {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.contact-hero .container {
    position: relative;
    z-index: 2;
}

.contact-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 28px;
    box-shadow:
        0 25px 50px rgba(30, 41, 59, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(100, 116, 139, 0.3), transparent);
}

.contact-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 35px 70px rgba(30, 41, 59, 0.15),
        0 0 0 1px rgba(100, 116, 139, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(100, 116, 139, 0.3);
}

.form-control {
    border: 2px solid #e2e8f0;
    border-radius: 20px;
    padding: 18px 24px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.95);
    font-size: 1rem;
    color: #334155;
    box-shadow:
        0 4px 6px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.form-control::placeholder {
    color: #94a3b8;
    opacity: 0.8;
}

.form-control:focus {
    border-color: #64748b;
    box-shadow:
        0 0 0 0.25rem rgba(100, 116, 139, 0.12),
        0 8px 25px rgba(100, 116, 139, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    background: #fff;
    transform: translateY(-2px) scale(1.01);
    outline: none;
}

.form-control:hover:not(:focus) {
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.form-floating label {
    color: #64748b;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.8) 20%, rgba(255, 255, 255, 0.8) 80%, transparent 100%);
    padding: 0 8px;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: #475569;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    font-weight: 600;
}

.form-floating > .form-control:focus ~ label {
    color: #334155;
}

.btn-modern {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border: none;
    border-radius: 50px;
    padding: 18px 45px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 15px 35px rgba(71, 85, 105, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    color: white;
    text-transform: none;
    font-size: 1.05rem;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.25), transparent);
    transition: left 0.6s ease;
}

.btn-modern::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover::after {
    width: 300px;
    height: 300px;
}

.btn-modern:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow:
        0 25px 50px rgba(71, 85, 105, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
}

.btn-modern:active {
    transform: translateY(-2px) scale(1.02);
}

.contact-info-item {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    border-left: 5px solid #64748b;
    transition: all 0.4s ease;
    box-shadow: 0 8px 25px rgba(30, 41, 59, 0.05);
}

.contact-info-item:hover {
    transform: translateX(8px);
    box-shadow: 0 15px 35px rgba(30, 41, 59, 0.1);
    border-left-color: #475569;
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-right: 24px;
    box-shadow:
        0 20px 40px rgba(71, 85, 105, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.contact-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
}

.contact-icon:hover::before {
    transform: scale(1);
}

.contact-icon:hover {
    transform: translateY(-5px) scale(1.08) rotate(5deg);
    box-shadow:
        0 25px 50px rgba(71, 85, 105, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.social-btn {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border: none;
    border-radius: 50px;
    padding: 12px 24px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    margin: 8px;
    font-weight: 500;
    box-shadow: 0 8px 20px rgba(71, 85, 105, 0.2);
}

.social-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(71, 85, 105, 0.3);
    color: white;
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
}

.social-btn.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
}

.social-btn.facebook:hover {
    background: linear-gradient(135deg, #166fe5 0%, #1976d2 100%);
}

.social-btn.line {
    background: linear-gradient(135deg, #00c300 0%, #00e600 100%);
}

.social-btn.line:hover {
    background: linear-gradient(135deg, #00b300 0%, #00d600 100%);
}

.business-hours-card {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    border-radius: 24px;
    padding: 30px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(71, 85, 105, 0.2);
}

.business-hours-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.hours-item:last-child {
    border-bottom: none;
}

.status-badge {
    background: rgba(255,255,255,0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-badge.open {
    background: rgba(76, 175, 80, 0.3);
    color: #4caf50;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-elements::before,
.floating-elements::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.floating-elements::before {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-elements::after {
    bottom: 10%;
    right: 10%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

.section-title {
    color: #1e293b;
    font-weight: 700;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border-radius: 2px;
}

/* Hero Styling */
.hero-badge {
    display: inline-block;
    margin-bottom: 1rem;
}

.badge-text {
    background: rgba(100, 116, 139, 0.1);
    color: #475569;
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    border: 1px solid rgba(100, 116, 139, 0.2);
    backdrop-filter: blur(10px);
}

.hero-title {
    color: #1e293b;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    line-height: 1.2;
}

.text-gradient {
    background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    color: #475569;
    font-size: 1.4rem;
    line-height: 1.6;
    font-weight: 400;
}

.highlight-text {
    color: #334155;
    font-weight: 600;
    position: relative;
}

.highlight-text::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border-radius: 1px;
}

/* Hero Icon Animation */
.hero-icon-container {
    position: relative;
    display: inline-block;
}

.icon-wrapper {
    position: relative;
    display: inline-block;
    animation: heroFloat 6s ease-in-out infinite;
}

.icon-wrapper i {
    color: #475569;
    opacity: 0.9;
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 15px 25px rgba(71, 85, 105, 0.2));
}

.icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(100, 116, 139, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: glow 4s ease-in-out infinite alternate;
    z-index: 1;
}

@keyframes heroFloat {
    0%, 100% { transform: translateY(0px) scale(1) rotate(0deg); }
    50% { transform: translateY(-20px) scale(1.05) rotate(5deg); }
}

@keyframes glow {
    0% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
    100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.2); }
}

/* Contact Stats and Features */
.contact-stats {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 24px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow:
        0 10px 30px rgba(30, 41, 59, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.contact-feature {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1.8rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
}

.contact-feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.contact-feature:hover::before {
    left: 100%;
}

.contact-feature:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateX(12px) scale(1.02);
    border-color: rgba(100, 116, 139, 0.25);
    box-shadow: 0 8px 25px rgba(30, 41, 59, 0.1);
}

.contact-feature i {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(100, 116, 139, 0.2) 0%, rgba(71, 85, 105, 0.3) 100%);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.8rem;
    font-size: 1.5rem;
    color: #475569;
    transition: all 0.4s ease;
    box-shadow:
        0 4px 12px rgba(71, 85, 105, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
}

.contact-feature i::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    border-radius: 18px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.contact-feature:hover i {
    background: linear-gradient(135deg, rgba(100, 116, 139, 0.3) 0%, rgba(71, 85, 105, 0.4) 100%);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 20px rgba(71, 85, 105, 0.2);
}

.contact-feature:hover i::before {
    opacity: 1;
}

.contact-feature h6 {
    color: #1e293b;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.contact-feature small {
    color: #64748b;
    font-weight: 500;
}

/* Contact Info Cards */
.contact-info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.4s ease;
    box-shadow: 0 15px 35px rgba(30, 41, 59, 0.08);
    position: relative;
    overflow: hidden;
}

.contact-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(100, 116, 139, 0.1), transparent);
    transition: left 0.5s ease;
}

.contact-info-card:hover::before {
    left: 100%;
}

.contact-info-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 55px rgba(30, 41, 59, 0.12);
    border-color: rgba(100, 116, 139, 0.2);
}

/* Hero Card */
.contact-hero-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 32px;
    padding: 3.5rem;
    text-align: center;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 25px 50px rgba(30, 41, 59, 0.12),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.contact-hero-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(100, 116, 139, 0.3), transparent);
}

.contact-hero-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 35px 70px rgba(30, 41, 59, 0.18),
        0 0 0 1px rgba(100, 116, 139, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.hero-card-icon {
    position: relative;
    display: inline-block;
}

.icon-circle {
    position: relative;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow:
        0 20px 40px rgba(71, 85, 105, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
}

.icon-circle i {
    color: white;
    font-size: 2.5rem;
    position: relative;
    z-index: 2;
}

.icon-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(100, 116, 139, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.3;
    }
}

.contact-hero-card:hover .icon-circle {
    transform: scale(1.1) rotate(10deg);
    box-shadow:
        0 25px 50px rgba(71, 85, 105, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.hero-card-title {
    color: #1e293b;
    font-weight: 700;
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.hero-card-subtitle {
    color: #475569;
    font-size: 1.2rem;
    line-height: 1.6;
}

.phone-number {
    font-weight: 700;
    color: #334155;
    font-size: 1.3rem;
}

.btn-outline-modern {
    background: transparent;
    border: 2px solid #64748b;
    color: #64748b;
    border-radius: 50px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-outline-modern:hover {
    background: #64748b;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.3);
}

.mini-feature {
    background: rgba(100, 116, 139, 0.1);
    border-radius: 12px;
    padding: 8px;
    font-size: 0.85rem;
    color: #475569;
    font-weight: 600;
    transition: all 0.3s ease;
}

.mini-feature:hover {
    background: rgba(100, 116, 139, 0.2);
    transform: translateY(-2px);
}

.mini-feature i {
    margin-right: 4px;
    font-size: 0.9rem;
}

/* Form Header Styling */
.form-header-icon {
    position: relative;
    display: inline-block;
}

.icon-container {
    position: relative;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.icon-container i {
    color: #64748b;
    opacity: 0.9;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.icon-bg {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(100, 116, 139, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: iconBreathe 4s ease-in-out infinite;
}

@keyframes iconBreathe {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.6;
    }
}

.form-subtitle {
    color: #475569;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 0;
}

.security-badge {
    display: inline-block;
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-top: 8px;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

/* Pulse Animation */
.pulse-animation {
    animation: pulse 3s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(100, 116, 139, 0.4);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(100, 116, 139, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(100, 116, 139, 0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-hero {
        min-height: 60vh;
        padding: 2rem 0;
    }

    .contact-card {
        margin: 1rem;
        padding: 1.5rem !important;
        border-radius: 20px;
    }

    .contact-info-card {
        margin-bottom: 2rem;
        padding: 2rem;
    }

    .floating-elements::before,
    .floating-elements::after {
        width: 150px;
        height: 150px;
    }

    .contact-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}
</style>
@endpush

@section('content')
<!-- Modern Hero Section -->
<section class="contact-hero position-relative">
    <div class="floating-elements"></div>
    <div class="container position-relative">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="hero-content">
                    <div class="hero-badge mb-4">
                        <span class="badge-text">
                            <i class="fas fa-star me-2"></i>บริการมืออาชีพ
                        </span>
                    </div>
                    <div class="hero-icon-container mb-4">
                        <div class="icon-wrapper">
                            <i class="fas fa-comments fa-5x"></i>
                            <div class="icon-glow"></div>
                        </div>
                    </div>
                    <h1 class="display-2 fw-bold mb-4 hero-title">
                        <span class="text-gradient">ติดต่อเรา</span>
                    </h1>
                    <p class="lead mb-5 hero-subtitle">
                        เราพร้อมให้คำปรึกษาและดูแลท่านด้วยความเอาใจใส่
                        <br><span class="highlight-text">ติดต่อเราได้ตลอด 24 ชั่วโมง</span>
                    </p>
                    <div class="contact-stats">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="contact-feature">
                                    <i class="fas fa-clock"></i>
                                    <div>
                                        <h6 class="mb-1" style="color: #1e293b;">ตอบกลับเร็ว</h6>
                                        <small style="color: #64748b;">ภายใน 24 ชั่วโมง</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="contact-feature">
                                    <i class="fas fa-shield-alt"></i>
                                    <div>
                                        <h6 class="mb-1" style="color: #1e293b;">ข้อมูลปลอดภัย</h6>
                                        <small style="color: #64748b;">เข้ารหัส 100%</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="contact-feature">
                                    <i class="fas fa-headset"></i>
                                    <div>
                                        <h6 class="mb-1" style="color: #1e293b;">บริการ 24/7</h6>
                                        <small style="color: #64748b;">พร้อมให้คำปรึกษา</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="contact-feature">
                                    <i class="fas fa-heart"></i>
                                    <div>
                                        <h6 class="mb-1" style="color: #1e293b;">ดูแลด้วยใจ</h6>
                                        <small style="color: #64748b;">ประสบการณ์ 20+ ปี</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image text-center">
                    <div class="contact-hero-card">
                        <div class="hero-card-icon mb-4">
                            <div class="icon-circle">
                                <i class="fas fa-phone-alt fa-3x"></i>
                                <div class="icon-pulse"></div>
                            </div>
                        </div>
                        <h3 class="hero-card-title mb-3">โทรหาเราได้เลย</h3>
                        <p class="hero-card-subtitle mb-4">
                            <span class="phone-number">{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}</span>
                            <br><small class="text-muted">บริการตลอด 24 ชั่วโมง</small>
                        </p>
                        <div class="hero-card-actions">
                            <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-modern pulse-animation mb-3">
                                <i class="fas fa-phone me-2"></i>โทรเลย
                            </a>
                            @if(!empty($settings['line_id']))
                            <br>
                            <a href="https://line.me/ti/p/{{ $settings['line_id'] }}" class="btn btn-outline-modern" target="_blank">
                                <i class="fab fa-line me-2"></i>แชท Line
                            </a>
                            @endif
                        </div>
                        <div class="hero-card-features mt-4">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="mini-feature">
                                        <i class="fas fa-clock"></i>
                                        <span>24/7</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mini-feature">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>ปลอดภัย</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Contact Section -->
<section class="py-5 bg-light">
    <div class="container">
        @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show contact-card" role="alert">
            <div class="d-flex align-items-center">
                <div class="contact-icon me-3">
                    <i class="fas fa-check"></i>
                </div>
                <div>
                    <h6 class="mb-0">ส่งข้อความสำเร็จ!</h6>
                    <small>{{ session('success') }}</small>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        @endif

        <div class="row g-5">
            <!-- Modern Contact Form -->
            <div class="col-lg-8">
                <div class="contact-card p-5">
                    <div class="text-center mb-5">
                        <div class="form-header-icon mb-4">
                            <div class="icon-container">
                                <i class="fas fa-paper-plane fa-3x"></i>
                                <div class="icon-bg"></div>
                            </div>
                        </div>
                        <h3 class="section-title">ส่งข้อความถึงเรา</h3>
                        <p class="form-subtitle">
                            กรอกข้อมูลด้านล่าง เราจะติดต่อกลับภายใน 24 ชั่วโมง
                            <br><span class="security-badge">
                                <i class="fas fa-shield-alt me-1"></i>ข้อมูลของคุณจะถูกเก็บเป็นความลับ
                            </span>
                        </p>
                    </div>

                    <form action="{{ route('contact.store') }}" method="POST" id="contactForm">
                        @csrf
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name') }}" placeholder="ชื่อ-นามสกุล" required>
                                    <label for="name">
                                        <i class="fas fa-user me-2"></i>ชื่อ-นามสกุล <span class="text-danger">*</span>
                                    </label>
                                    @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                           id="email" name="email" value="{{ old('email') }}" placeholder="อีเมล" required>
                                    <label for="email">
                                        <i class="fas fa-envelope me-2"></i>อีเมล <span class="text-danger">*</span>
                                    </label>
                                    @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                           id="phone" name="phone" value="{{ old('phone') }}" placeholder="เบอร์โทรศัพท์">
                                    <label for="phone">
                                        <i class="fas fa-phone me-2"></i>เบอร์โทรศัพท์
                                    </label>
                                    @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-control @error('subject') is-invalid @enderror" id="subject" name="subject" required>
                                        <option value="">เลือกหัวข้อ</option>
                                        <option value="สอบถามบริการ" {{ old('subject') == 'สอบถามบริการ' ? 'selected' : '' }}>สอบถามบริการ</option>
                                        <option value="สอบถามแพคเกจ" {{ old('subject') == 'สอบถามแพคเกจ' ? 'selected' : '' }}>สอบถามแพคเกจ</option>
                                        <option value="ขอใบเสนอราคา" {{ old('subject') == 'ขอใบเสนอราคา' ? 'selected' : '' }}>ขอใบเสนอราคา</option>
                                        <option value="ร้องเรียน/แนะนำ" {{ old('subject') == 'ร้องเรียน/แนะนำ' ? 'selected' : '' }}>ร้องเรียน/แนะนำ</option>
                                        <option value="อื่นๆ" {{ old('subject') == 'อื่นๆ' ? 'selected' : '' }}>อื่นๆ</option>
                                    </select>
                                    <label for="subject">
                                        <i class="fas fa-tag me-2"></i>หัวข้อ <span class="text-danger">*</span>
                                    </label>
                                    @error('subject')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-floating">
                                    <textarea class="form-control @error('message') is-invalid @enderror"
                                              id="message" name="message" placeholder="ข้อความ" style="height: 120px" required>{{ old('message') }}</textarea>
                                    <label for="message">
                                        <i class="fas fa-comment me-2"></i>ข้อความ <span class="text-danger">*</span>
                                    </label>
                                    @error('message')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-modern btn-lg px-5">
                                    <i class="fas fa-paper-plane me-2"></i>ส่งข้อความ
                                </button>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt me-1"></i>
                                        ข้อมูลของคุณจะถูกเก็บรักษาอย่างปลอดภัย
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Modern Contact Info -->
            <div class="col-lg-4">
                <!-- Contact Information Card -->
                <div class="contact-card p-4 mb-4">
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-address-book fa-3x" style="color: #64748b; opacity: 0.8;"></i>
                        </div>
                        <h4 class="section-title">ข้อมูลติดต่อ</h4>
                        <p class="text-muted">ช่องทางการติดต่อที่สะดวกสำหรับคุณ</p>
                    </div>

                    <div class="contact-info">
                        <div class="contact-info-item">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">ที่อยู่</h6>
                                    <p class="mb-0 text-muted small">{{ $settings['contact_address'] ?? 'ที่อยู่บริษัท' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">โทรศัพท์</h6>
                                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="text-decoration-none text-muted small">
                                        {{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">อีเมล</h6>
                                    <a href="mailto:{{ $settings['contact_email'] ?? '' }}" class="text-decoration-none text-muted small">
                                        {{ $settings['contact_email'] ?? '<EMAIL>' }}
                                    </a>
                                </div>
                            </div>
                        </div>

                        @if(!empty($settings['facebook_url']) || !empty($settings['line_id']))
                        <div class="contact-info-item">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon">
                                    <i class="fas fa-share-alt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-2 fw-bold">ติดตามเรา</h6>
                                    <div>
                                        @if(!empty($settings['facebook_url']))
                                        <a href="{{ $settings['facebook_url'] }}" class="social-btn facebook" target="_blank">
                                            <i class="fab fa-facebook me-2"></i>Facebook
                                        </a>
                                        @endif
                                        @if(!empty($settings['line_id']))
                                        <a href="https://line.me/ti/p/{{ $settings['line_id'] }}" class="social-btn line" target="_blank">
                                            <i class="fab fa-line me-2"></i>Line Official
                                        </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Modern Business Hours -->
                <div class="business-hours-card position-relative">
                    <div class="text-center mb-4">
                        <i class="fas fa-clock fa-2x mb-3" style="opacity: 0.8;"></i>
                        <h5 class="fw-bold">เวลาให้บริการ</h5>
                        <p class="mb-0 small" style="opacity: 0.9;">พร้อมให้บริการคุณตลอดเวลา</p>
                    </div>

                    <div class="text-center mb-4">
                        <div class="status-badge open">
                            <i class="fas fa-circle me-1"></i>
                            <strong>เปิดให้บริการ 24/7</strong>
                        </div>
                    </div>

                    <div class="hours-list">
                        <div class="hours-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-building me-3"></i>
                                <div>
                                    <strong>สำนักงาน</strong>
                                    <br><small style="opacity: 0.8;">จันทร์ - ศุกร์</small>
                                </div>
                            </div>
                            <span class="status-badge">08:00 - 17:00</span>
                        </div>

                        <div class="hours-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-ambulance me-3"></i>
                                <div>
                                    <strong>บริการฉุกเฉิน</strong>
                                    <br><small style="opacity: 0.8;">ตลอด 24 ชั่วโมง</small>
                                </div>
                            </div>
                            <span class="status-badge open">24/7</span>
                        </div>

                        <div class="hours-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-calendar-alt me-3"></i>
                                <div>
                                    <strong>วันหยุดนักขัตฤกษ์</strong>
                                    <br><small style="opacity: 0.8;">ให้บริการปกติ</small>
                                </div>
                            </div>
                            <span class="status-badge open">เปิด</span>
                        </div>

                        <div class="hours-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-headset me-3"></i>
                                <div>
                                    <strong>ศูนย์บริการลูกค้า</strong>
                                    <br><small style="opacity: 0.8;">คำปรึกษาออนไลน์</small>
                                </div>
                            </div>
                            <span class="status-badge open">24/7</span>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <div class="d-grid gap-2">
                            <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-modern">
                                <i class="fas fa-phone me-2"></i>โทรเลย
                            </a>
                            @if(!empty($settings['line_id']))
                            <a href="https://line.me/ti/p/{{ $settings['line_id'] }}" class="social-btn line" target="_blank">
                                <i class="fab fa-line me-2"></i>แชท Line
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation and enhancement
    const form = document.getElementById('contactForm');
    const inputs = form.querySelectorAll('input, textarea, select');

    // Add floating label animation
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
            this.style.transform = 'translateY(-2px)';
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
            this.style.transform = 'translateY(0)';
        });

        // Check if input has value on load
        if (input.value) {
            input.parentElement.classList.add('focused');
        }

        // Add typing animation
        input.addEventListener('input', function() {
            this.style.borderColor = '#64748b';
        });
    });

    // Form submission with loading state and animation
    form.addEventListener('submit', function(e) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        // Add loading animation
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังส่ง...';
        submitBtn.disabled = true;
        submitBtn.style.transform = 'scale(0.95)';

        // Add success animation after a short delay
        setTimeout(() => {
            submitBtn.style.transform = 'scale(1)';
        }, 200);

        // Re-enable button after 3 seconds (in case of error)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Add smooth scroll to form if there are errors
    @if($errors->any())
    document.getElementById('contactForm').scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    });
    @endif

    // Enhanced intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) scale(1)';
                    entry.target.classList.add('animate-in');
                }, index * 100); // Stagger animation
            }
        });
    }, observerOptions);

    // Observe contact cards with enhanced styling
    document.querySelectorAll('.contact-card, .contact-info-card, .business-hours-card, .contact-hero-card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px) scale(0.95)';
        card.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
        observer.observe(card);
    });

    // Observe contact features with stagger effect
    document.querySelectorAll('.contact-feature').forEach((feature, index) => {
        feature.style.opacity = '0';
        feature.style.transform = 'translateX(-30px)';
        feature.style.transition = `all 0.6s cubic-bezier(0.4, 0, 0.2, 1) ${index * 0.1}s`;
        observer.observe(feature);
    });

    // Enhanced hover effects for contact info items
    document.querySelectorAll('.contact-info-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(12px) scale(1.03)';
            this.style.boxShadow = '0 15px 35px rgba(30, 41, 59, 0.15)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(8px) scale(1)';
            this.style.boxShadow = '0 8px 25px rgba(30, 41, 59, 0.05)';
        });
    });

    // Enhanced click to copy functionality with better UX
    document.querySelectorAll('[href^="tel:"], [href^="mailto:"]').forEach(link => {
        link.addEventListener('click', function(e) {
            const text = this.textContent.trim();
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    // Show enhanced tooltip
                    const tooltip = document.createElement('div');
                    tooltip.innerHTML = '<i class="fas fa-check me-2"></i>คัดลอกแล้ว!';
                    tooltip.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%) scale(0.8);
                        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                        color: white;
                        padding: 12px 20px;
                        border-radius: 25px;
                        font-size: 14px;
                        font-weight: 600;
                        z-index: 9999;
                        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
                        animation: tooltipShow 2.5s cubic-bezier(0.4, 0, 0.2, 1);
                    `;
                    document.body.appendChild(tooltip);
                    setTimeout(() => tooltip.remove(), 2500);
                });
            }
        });
    });

    // Add parallax effect to floating elements
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.floating-elements');
        if (parallax) {
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        }
    });

    // Add smooth reveal animation for form inputs
    const formInputs = document.querySelectorAll('.form-floating');
    formInputs.forEach((input, index) => {
        input.style.opacity = '0';
        input.style.transform = 'translateY(20px)';
        input.style.transition = `all 0.6s cubic-bezier(0.4, 0, 0.2, 1) ${index * 0.1}s`;

        setTimeout(() => {
            input.style.opacity = '1';
            input.style.transform = 'translateY(0)';
        }, 500 + (index * 100));
    });
});

// Add enhanced CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes tooltipShow {
        0% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5) rotate(-10deg);
        }
        20% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.1) rotate(5deg);
        }
        40% {
            transform: translate(-50%, -50%) scale(1) rotate(0deg);
        }
        80% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1) rotate(0deg);
        }
        100% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
        }
    }

    .animate-in {
        animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .contact-card:hover .form-header-icon .icon-container i {
        transform: scale(1.1) rotate(10deg);
        color: #475569;
    }

    .contact-card:hover .icon-bg {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.8;
    }
`;
document.head.appendChild(style);
</script>
@endpush

@endsection
